定义：针对代理商以及代理商下面的业务员去代理商网页后台或者手机司南APP上给商家开通付呗账号的时候提交资料会出现的审核报错以及报错的原因和解决办法、操作步骤。

# <font style="color:#000000;">Q:报错：法人手机号运营商鉴权失败[不一致[01]]</font>
![](https://cdn.nlark.com/yuque/0/2025/png/21499923/1747798156908-9c9447b7-af57-42a9-a9f3-5f93c02c3432.png)

<font style="color:#DF2A3F;">A:易生小微商户进件会校验手机号和法人是否一致，不一致就会报这个错。业务员如果用自己手机号给商户开易生小微商户就会报错，报错了就需要用商户的真实手机号重新注册开户。</font>

# <font style="color:rgb(38, 38, 38);">Q:报错：审核成功[0000];X-参数错误@address info.address-1001;</font>
![](https://cdn.nlark.com/yuque/0/2025/png/21499923/1747798261738-52f005fe-0021-4874-98e8-777c2197c3d3.png)

<font style="color:#DF2A3F;">A:地址有问题，去易生后台修改地址，详细地址里面不能有省市，修改通过以后拆分</font>

# Q:报错：<font style="color:rgb(38, 38, 38);">审核成功[0000];微信拆分错误:不允许开通的商户类型:7997[1031]</font>
![](https://cdn.nlark.com/yuque/0/2025/png/21499923/1747798358248-42ab94f2-02aa-48aa-9e43-56889dae4eb8.png)

<font style="color:#DF2A3F;">A:类目选择错了，反馈给易生群对接沟通处理</font>

# Q:报错：<font style="color:rgb(38, 38, 38);">【无效参数】参数名：content，原因：must not be null</font>
![](https://cdn.nlark.com/yuque/0/2025/png/21499923/1747798430008-bda02c29-8d61-415a-91fb-dd101852af5c.png)

<font style="color:#DF2A3F;">A:检查一下商户进件资料有木有带了空格和特殊字符，尤其是经营地址这一栏，如确有特殊字符，重新开户并检查输入的地址</font>

