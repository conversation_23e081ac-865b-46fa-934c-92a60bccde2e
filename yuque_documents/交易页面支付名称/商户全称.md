### 商户全称是做微信支付宝认证的执照主体名称 ，微信的可以修改 ，支付宝不支持
![](https://cdn.nlark.com/yuque/0/2025/png/32542927/1750304085319-950990a9-4360-43eb-a185-4e687cecb003.png?x-oss-process=image%2Fformat%2Cwebp)

### 微信全称修改步骤
1.首先确认客户想修改的名称 是否有对应的执照主体名称，小微的只能改成商户_xx（法人姓名）

2.先让客户取消关联商户号

<font style="color:rgb(51, 51, 51);">联系人（扫码认证的人）扫对应的拓展码进入“微信支付商家助手”小程序--点击小程序--选择对应的商户--已核实商户号--选择不需要的商户号点击“</font><font style="color:rgb(237, 95, 95);">取消关联</font><font style="color:rgb(51, 51, 51);">”</font>

![](https://cdn.nlark.com/yuque/0/2025/png/32542927/1750304328446-03b6c4e6-8ee3-4c86-addc-34eb89cc1cbf.png)<font style="color:rgb(51, 51, 51);">  
</font>

<font style="color:rgb(51, 51, 51);">注：</font><font style="color:rgb(237, 95, 95);">取消关联</font><font style="color:rgb(51, 51, 51);">会影响该门店不能使用，请确认清楚再</font><font style="color:rgb(237, 95, 95);">取消</font>

3.取消关联后在crm-商户管理-商户信息号查询-间联商户号查询找到他想修改的门店的商户号

![](https://cdn.nlark.com/yuque/0/2025/png/32542927/1750304470111-80767c35-45e0-49d1-9d0e-58bc9c58b6c0.png)



4.登录微信后台（网址 [https://pay.weixin.qq.com/index.php/extend/channel_sub_manage](https://pay.weixin.qq.com/index.php/extend/channel_sub_manage)）在合作伙伴功能-特约商户管理里-已创建商户号-修改



![](https://cdn.nlark.com/yuque/0/2025/png/32542927/1750304634581-f0a8f598-30cf-4e35-9caf-d25881e65a41.png)



5.修改成功后让他重新扫微信拓展码重新认证下对应商户号



